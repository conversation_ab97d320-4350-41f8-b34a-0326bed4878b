"""
API endpoints pour les calculs de ligne de vue (Line of Sight)
Optimisé pour les performances avec calculs asynchrones
"""

import asyncio
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field, validator
import structlog

from app.routers.auth import get_current_user, User

logger = structlog.get_logger()

router = APIRouter()

# Modèles Pydantic
class Point(BaseModel):
    """Point géographique"""
    lon: float = Field(..., ge=-180, le=180, description="Longitude en degrés")
    lat: float = Field(..., ge=-90, le=90, description="Latitude en degrés")
    
    @validator('lon')
    def validate_longitude(cls, v):
        if not -180 <= v <= 180:
            raise ValueError('Longitude doit être entre -180 et 180')
        return v
    
    @validator('lat')
    def validate_latitude(cls, v):
        if not -90 <= v <= 90:
            raise ValueError('Latitude doit être entre -90 et 90')
        return v

class LineOfSightRequest(BaseModel):
    """Requête de calcul de ligne de vue"""
    observer: Point = Field(..., description="Position de l'observateur")
    target: Point = Field(..., description="Position de la cible")
    observer_height: float = Field(2.0, ge=0, le=100, description="Hauteur de l'observateur en mètres")
    target_height: float = Field(0.0, ge=0, le=100, description="Hauteur de la cible en mètres")
    
    @validator('observer_height', 'target_height')
    def validate_heights(cls, v):
        if v < 0 or v > 100:
            raise ValueError('Hauteur doit être entre 0 et 100 mètres')
        return v

class LineOfSightResponse(BaseModel):
    """Réponse du calcul de ligne de vue"""
    visible: bool = Field(..., description="Visibilité entre les deux points")
    distance_m: float = Field(..., description="Distance en mètres")
    observer_elevation_m: Optional[float] = Field(None, description="Élévation de l'observateur")
    target_elevation_m: Optional[float] = Field(None, description="Élévation de la cible")
    obstruction_distance_m: Optional[float] = Field(None, description="Distance de l'obstruction")
    obstruction_elevation_m: Optional[float] = Field(None, description="Élévation de l'obstruction")
    required_elevation_m: Optional[float] = Field(None, description="Élévation requise pour la visibilité")
    reason: Optional[str] = Field(None, description="Raison de la non-visibilité")
    calculation_time_ms: Optional[float] = Field(None, description="Temps de calcul en millisecondes")

class ElevationRequest(BaseModel):
    """Requête d'élévation pour un point"""
    point: Point = Field(..., description="Point géographique")

class ElevationResponse(BaseModel):
    """Réponse d'élévation"""
    elevation_m: Optional[float] = Field(None, description="Élévation en mètres")
    point: Point = Field(..., description="Point demandé")

class MNTStatsResponse(BaseModel):
    """Statistiques du service MNT"""
    total_files: int = Field(..., description="Nombre total de fichiers MNT")
    cache_size: int = Field(..., description="Nombre de fichiers en cache")
    index_loaded: bool = Field(..., description="Index spatial chargé")

# Cache pour les résultats récents
recent_calculations: Dict[str, LineOfSightResponse] = {}
MAX_CACHE_SIZE = 100

def get_cache_key(request: LineOfSightRequest) -> str:
    """Générer une clé de cache pour la requête"""
    return f"{request.observer.lon:.6f},{request.observer.lat:.6f}-{request.target.lon:.6f},{request.target.lat:.6f}-{request.observer_height}-{request.target_height}"

async def calculate_line_of_sight_async(request: LineOfSightRequest) -> LineOfSightResponse:
    """Calcul asynchrone de ligne de vue"""
    import time
    start_time = time.time()
    
    try:
        # Vérifier le cache
        cache_key = get_cache_key(request)
        if cache_key in recent_calculations:
            logger.debug("Résultat trouvé en cache")
            result = recent_calculations[cache_key]
            result.calculation_time_ms = (time.time() - start_time) * 1000
            return result
        
        # Effectuer le calcul dans un thread séparé pour éviter de bloquer
        loop = asyncio.get_event_loop()
        result_dict = await loop.run_in_executor(
            None,
            mnt_service.calculate_line_of_sight,
            request.observer.lon,
            request.observer.lat,
            request.target.lon,
            request.target.lat,
            request.observer_height,
            request.target_height
        )
        
        # Créer la réponse
        calculation_time = (time.time() - start_time) * 1000
        result_dict['calculation_time_ms'] = calculation_time
        
        response = LineOfSightResponse(**result_dict)
        
        # Mettre en cache
        if len(recent_calculations) >= MAX_CACHE_SIZE:
            # Supprimer le plus ancien
            oldest_key = next(iter(recent_calculations))
            del recent_calculations[oldest_key]
        
        recent_calculations[cache_key] = response
        
        logger.info(
            "Calcul de ligne de vue terminé",
            visible=response.visible,
            distance_m=response.distance_m,
            calculation_time_ms=calculation_time
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Erreur lors du calcul de ligne de vue: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors du calcul de visibilité: {str(e)}"
        )

@router.post("/line-of-sight", response_model=LineOfSightResponse)
async def calculate_line_of_sight(
    request: LineOfSightRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Calculer la ligne de vue entre deux points
    
    - **observer**: Position de l'observateur (longitude, latitude)
    - **target**: Position de la cible (longitude, latitude)  
    - **observer_height**: Hauteur de l'observateur en mètres (défaut: 2.0)
    - **target_height**: Hauteur de la cible en mètres (défaut: 0.0)
    
    Retourne la visibilité, la distance et les détails d'obstruction éventuelle.
    """
    logger.info(
        "Demande de calcul de ligne de vue",
        user=current_user.username,
        observer=f"{request.observer.lon:.6f},{request.observer.lat:.6f}",
        target=f"{request.target.lon:.6f},{request.target.lat:.6f}"
    )
    
    # Vérifier que le service MNT est disponible
    if not mnt_service.spatial_index:
        raise HTTPException(
            status_code=503,
            detail="Service MNT non disponible. Veuillez initialiser l'index spatial."
        )
    
    return await calculate_line_of_sight_async(request)

@router.post("/elevation", response_model=ElevationResponse)
async def get_elevation(
    request: ElevationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Obtenir l'élévation à un point donné
    
    - **point**: Point géographique (longitude, latitude)
    
    Retourne l'élévation en mètres ou null si non disponible.
    """
    try:
        elevation = mnt_service.get_elevation_at_point(
            request.point.lon,
            request.point.lat
        )
        
        return ElevationResponse(
            elevation_m=elevation,
            point=request.point
        )
        
    except Exception as e:
        logger.error(f"Erreur lors de l'obtention de l'élévation: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de l'obtention de l'élévation: {str(e)}"
        )

@router.get("/mnt-stats", response_model=MNTStatsResponse)
async def get_mnt_stats(current_user: User = Depends(get_current_user)):
    """
    Obtenir les statistiques du service MNT
    """
    try:
        return MNTStatsResponse(
            total_files=len(mnt_service.file_metadata),
            cache_size=len(mnt_service.file_handles),
            index_loaded=mnt_service.spatial_index is not None
        )
    except Exception as e:
        logger.error(f"Erreur lors de l'obtention des statistiques MNT: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de l'obtention des statistiques: {str(e)}"
        )

@router.post("/clear-cache")
async def clear_cache(current_user: User = Depends(get_current_user)):
    """
    Vider le cache des calculs récents
    Nécessite les droits d'administrateur
    """
    if current_user.role not in ['admin', 'operator']:
        raise HTTPException(
            status_code=403,
            detail="Droits insuffisants pour vider le cache"
        )
    
    global recent_calculations
    recent_calculations.clear()
    
    logger.info("Cache des calculs vidé", user=current_user.username)
    return {"message": "Cache vidé avec succès"}

@router.post("/reload-index")
async def reload_mnt_index(current_user: User = Depends(get_current_user)):
    """
    Recharger l'index spatial MNT
    Nécessite les droits d'administrateur
    """
    if current_user.role != 'admin':
        raise HTTPException(
            status_code=403,
            detail="Droits d'administrateur requis pour recharger l'index"
        )
    
    try:
        success = mnt_service.load_index()
        if success:
            logger.info("Index MNT rechargé", user=current_user.username)
            return {"message": "Index MNT rechargé avec succès"}
        else:
            raise HTTPException(
                status_code=500,
                detail="Échec du rechargement de l'index MNT"
            )
    except Exception as e:
        logger.error(f"Erreur lors du rechargement de l'index: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors du rechargement: {str(e)}"
        )
