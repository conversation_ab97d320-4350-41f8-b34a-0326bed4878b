#!/usr/bin/env python3
"""
Router des équipements pour C2-EW Platform
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any

from app.routers.auth import get_current_user, User

router = APIRouter()

# Modèles Pydantic
class Equipment(BaseModel):
    id: int
    name: str
    type: str
    status: str
    description: str
    location: Dict[str, float]
    metrics: Optional[Dict[str, Any]] = None

# Données mockées des équipements
MOCK_EQUIPMENT = [
    {
        "id": 1,
        "name": "ICOM IC-R8600",
        "type": "comint",
        "status": "active",
        "description": "Récepteur de communication large bande",
        "location": {"latitude": 48.8566, "longitude": 2.3522},
        "metrics": {
            "frequency": "145.500 MHz",
            "signal_strength": "-65 dBm",
            "mode": "FM",
            "bandwidth": "12.5 kHz"
        }
    },
    {
        "id": 2,
        "name": "Anti-Drone System",
        "type": "anti_drone",
        "status": "standby",
        "description": "Système de détection et neutralisation de drones",
        "location": {"latitude": 48.8606, "longitude": 2.3376},
        "metrics": {
            "detection_range": "2 km",
            "targets_tracked": 0,
            "jamming_power": "50W"
        }
    },
    {
        "id": 3,
        "name": "ELINT Sensor",
        "type": "elint",
        "status": "active",
        "description": "Capteur de renseignement électronique",
        "location": {"latitude": 48.8534, "longitude": 2.3488},
        "metrics": {
            "frequency_range": "1-18 GHz",
            "signals_detected": 15,
            "analysis_mode": "Auto"
        }
    },
    {
        "id": 4,
        "name": "RF Jammer",
        "type": "jammer",
        "status": "offline",
        "description": "Brouilleur de fréquences radio",
        "location": {"latitude": 48.8500, "longitude": 2.3400},
        "metrics": {
            "power_output": "100W",
            "frequency_bands": ["VHF", "UHF"],
            "coverage_radius": "5 km"
        }
    }
]

# Routes des équipements
@router.get("/", response_model=List[Equipment])
async def get_equipment(equipment_type: str = None, current_user: User = Depends(get_current_user)):
    """Récupérer tous les équipements ou filtrer par type"""
    if equipment_type:
        filtered_equipment = [eq for eq in MOCK_EQUIPMENT if eq["type"] == equipment_type]
        return [Equipment(**eq) for eq in filtered_equipment]
    return [Equipment(**eq) for eq in MOCK_EQUIPMENT]

@router.get("/{equipment_id}", response_model=Equipment)
async def get_equipment_by_id(equipment_id: int, current_user: User = Depends(get_current_user)):
    """Récupérer un équipement par ID"""
    equipment = next((eq for eq in MOCK_EQUIPMENT if eq["id"] == equipment_id), None)
    if not equipment:
        raise HTTPException(status_code=404, detail="Équipement non trouvé")
    return Equipment(**equipment)

@router.post("/{equipment_id}/command")
async def send_equipment_command(
    equipment_id: int, 
    command: Dict[str, Any], 
    current_user: User = Depends(get_current_user)
):
    """Envoyer une commande à un équipement"""
    equipment = next((eq for eq in MOCK_EQUIPMENT if eq["id"] == equipment_id), None)
    if not equipment:
        raise HTTPException(status_code=404, detail="Équipement non trouvé")
    
    return {
        "message": f"Commande envoyée à {equipment['name']}",
        "command": command,
        "status": "success"
    }
