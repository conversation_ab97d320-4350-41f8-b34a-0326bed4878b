#!/usr/bin/env python3
"""
Modèles de données pour C2-EW Platform
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Optional

Base = declarative_base()

class User(Base):
    """Modèle utilisateur"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    role = Column(String(20), nullable=False, default="viewer")
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Equipment(Base):
    """Modèle équipement"""
    __tablename__ = "equipment"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    type = Column(String(50), nullable=False)  # comint, elint, anti_drone, jammer
    status = Column(String(20), nullable=False, default="offline")  # active, standby, offline
    description = Column(Text)
    latitude = Column(Float)
    longitude = Column(Float)
    altitude = Column(Float, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Alert(Base):
    """Modèle alerte"""
    __tablename__ = "alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)
    message = Column(Text, nullable=False)
    level = Column(String(20), nullable=False)  # info, warning, critical
    status = Column(String(20), nullable=False, default="unread")  # unread, read, acknowledged
    equipment_id = Column(Integer, ForeignKey("equipment.id"))
    latitude = Column(Float)
    longitude = Column(Float)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    equipment = relationship("Equipment", backref="alerts")

class Command(Base):
    """Modèle commande"""
    __tablename__ = "commands"
    
    id = Column(Integer, primary_key=True, index=True)
    equipment_id = Column(Integer, ForeignKey("equipment.id"), nullable=False)
    command_type = Column(String(50), nullable=False)
    parameters = Column(Text)  # JSON string
    status = Column(String(20), nullable=False, default="pending")  # pending, executing, completed, failed, cancelled
    result = Column(Text)  # JSON string
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    executed_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Relations
    equipment = relationship("Equipment", backref="commands")
    user = relationship("User", backref="commands")

class Plugin(Base):
    """Modèle plugin"""
    __tablename__ = "plugins"
    
    id = Column(String(50), primary_key=True)
    name = Column(String(100), nullable=False)
    display_name = Column(String(100), nullable=False)
    version = Column(String(20), nullable=False)
    description = Column(Text)
    author = Column(String(100))
    status = Column(String(20), nullable=False, default="inactive")  # active, inactive
    supported_equipment_types = Column(Text)  # JSON array
    commands = Column(Text)  # JSON array
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Zone(Base):
    """Modèle zone géographique"""
    __tablename__ = "zones"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    type = Column(String(50), nullable=False)  # surveillance, restricted, operational
    description = Column(Text)
    coordinates = Column(Text, nullable=False)  # JSON array of [lat, lon] pairs
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Log(Base):
    """Modèle log système"""
    __tablename__ = "logs"
    
    id = Column(Integer, primary_key=True, index=True)
    level = Column(String(20), nullable=False)  # debug, info, warning, error, critical
    message = Column(Text, nullable=False)
    module = Column(String(100))
    user_id = Column(Integer, ForeignKey("users.id"))
    equipment_id = Column(Integer, ForeignKey("equipment.id"))
    metadata = Column(Text)  # JSON string
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    user = relationship("User", backref="logs")
    equipment = relationship("Equipment", backref="logs")
