#!/usr/bin/env python3
"""
Router des commandes pour C2-EW Platform
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.routers.auth import get_current_user, User

router = APIRouter()

# Modèles Pydantic
class Command(BaseModel):
    id: int
    equipment_id: int
    command_type: str
    parameters: Dict[str, Any]
    status: str
    created_at: str
    executed_at: Optional[str] = None
    result: Optional[Dict[str, Any]] = None

class CommandCreate(BaseModel):
    equipment_id: int
    command_type: str
    parameters: Dict[str, Any]

# Données mockées des commandes
MOCK_COMMANDS = [
    {
        "id": 1,
        "equipment_id": 1,
        "command_type": "set_frequency",
        "parameters": {"frequency": "145.500"},
        "status": "completed",
        "created_at": datetime.now().isoformat(),
        "executed_at": datetime.now().isoformat(),
        "result": {"success": True, "message": "Fréquence changée"}
    },
    {
        "id": 2,
        "equipment_id": 2,
        "command_type": "start_detection",
        "parameters": {"zone_radius": 2000},
        "status": "pending",
        "created_at": datetime.now().isoformat(),
        "executed_at": None,
        "result": None
    }
]

# Routes des commandes
@router.get("/", response_model=List[Command])
async def get_commands(current_user: User = Depends(get_current_user)):
    """Récupérer toutes les commandes"""
    return [Command(**cmd) for cmd in MOCK_COMMANDS]

@router.get("/{command_id}", response_model=Command)
async def get_command_by_id(command_id: int, current_user: User = Depends(get_current_user)):
    """Récupérer une commande par ID"""
    command = next((c for c in MOCK_COMMANDS if c["id"] == command_id), None)
    if not command:
        raise HTTPException(status_code=404, detail="Commande non trouvée")
    return Command(**command)

@router.post("/", response_model=Command)
async def create_command(command_data: CommandCreate, current_user: User = Depends(get_current_user)):
    """Créer une nouvelle commande"""
    new_id = max([c["id"] for c in MOCK_COMMANDS]) + 1 if MOCK_COMMANDS else 1
    
    new_command = {
        "id": new_id,
        "equipment_id": command_data.equipment_id,
        "command_type": command_data.command_type,
        "parameters": command_data.parameters,
        "status": "pending",
        "created_at": datetime.now().isoformat(),
        "executed_at": None,
        "result": None
    }
    
    MOCK_COMMANDS.append(new_command)
    return Command(**new_command)

@router.patch("/{command_id}/execute")
async def execute_command(command_id: int, current_user: User = Depends(get_current_user)):
    """Exécuter une commande"""
    command = next((c for c in MOCK_COMMANDS if c["id"] == command_id), None)
    if not command:
        raise HTTPException(status_code=404, detail="Commande non trouvée")
    
    if command["status"] != "pending":
        raise HTTPException(status_code=400, detail="Commande déjà exécutée ou annulée")
    
    # Simuler l'exécution
    command["status"] = "completed"
    command["executed_at"] = datetime.now().isoformat()
    command["result"] = {
        "success": True,
        "message": f"Commande {command['command_type']} exécutée avec succès"
    }
    
    return Command(**command)

@router.delete("/{command_id}")
async def cancel_command(command_id: int, current_user: User = Depends(get_current_user)):
    """Annuler une commande"""
    command = next((c for c in MOCK_COMMANDS if c["id"] == command_id), None)
    if not command:
        raise HTTPException(status_code=404, detail="Commande non trouvée")
    
    if command["status"] != "pending":
        raise HTTPException(status_code=400, detail="Impossible d'annuler une commande déjà exécutée")
    
    command["status"] = "cancelled"
    return {"message": "Commande annulée", "command_id": command_id}
