#!/usr/bin/env python3
"""
Router WebSocket pour C2-EW Platform
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from typing import List, Dict, Any
import json
import asyncio
from datetime import datetime
import jwt

from app.config import settings

router = APIRouter()

# Gestionnaire de connexions WebSocket
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.user_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        await websocket.accept()
        self.active_connections.append(websocket)
        self.user_connections[user_id] = websocket
        print(f"🔌 WebSocket connecté pour l'utilisateur: {user_id}")

    def disconnect(self, websocket: WebSocket, user_id: str):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        if user_id in self.user_connections:
            del self.user_connections[user_id]
        print(f"🔌 WebSocket déconnecté pour l'utilisateur: {user_id}")

    async def send_personal_message(self, message: str, user_id: str):
        if user_id in self.user_connections:
            await self.user_connections[user_id].send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # Connexion fermée, on l'ignore
                pass

manager = ConnectionManager()

# Fonction pour vérifier le token WebSocket
def verify_websocket_token(token: str) -> str:
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            print(f"❌ Token sans username: {payload}")
            raise Exception("Token invalide - pas de username")
        print(f"✅ Token valide pour: {username}")
        return username
    except jwt.ExpiredSignatureError:
        print("❌ Token expiré")
        raise Exception("Token expiré")
    except jwt.InvalidTokenError as e:
        print(f"❌ Token invalide: {e}")
        raise Exception(f"Token invalide: {e}")
    except Exception as e:
        print(f"❌ Erreur token: {e}")
        raise Exception(f"Erreur token: {e}")

# Routes WebSocket
@router.websocket("/")
async def websocket_endpoint(websocket: WebSocket, token: str = Query(None)):
    username = "anonymous"
    try:
        print(f"🔌 Tentative de connexion WebSocket avec token: {token[:50] if token else 'None'}...")

        # Accepter la connexion d'abord
        await websocket.accept()
        print("🔌 WebSocket accepté !")

        # Vérifier le token si fourni
        if token:
            try:
                username = verify_websocket_token(token)
                print(f"🔌 Token validé pour: {username}")
            except Exception as token_error:
                print(f"⚠️ Token invalide, connexion anonyme: {token_error}")
                username = "anonymous"
        else:
            print("🔌 Pas de token fourni, connexion anonyme")
            username = "anonymous"

        # Ajouter à la liste des connexions
        manager.active_connections.append(websocket)
        if username != "anonymous":
            manager.user_connections[username] = websocket
        print(f"🔌 WebSocket connecté pour: {username}")
        
        # Envoyer un message de bienvenue
        welcome_message = {
            "type": "welcome",
            "message": f"Connexion WebSocket établie pour {username}",
            "timestamp": datetime.now().isoformat(),
            "user": username
        }
        await websocket.send_text(json.dumps(welcome_message))
        
        # Note: Messages périodiques désactivés pour simplifier
        
        # Écouter les messages du client
        while True:
            data = await websocket.receive_text()
            try:
                message = json.loads(data)
                await handle_client_message(message, username, websocket)
            except json.JSONDecodeError:
                error_message = {
                    "type": "error",
                    "message": "Format de message invalide",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(error_message))
                
    except WebSocketDisconnect:
        print(f"🔌 WebSocket déconnecté pour: {username}")
        # Nettoyer les connexions
        if websocket in manager.active_connections:
            manager.active_connections.remove(websocket)
        if username and username in manager.user_connections:
            del manager.user_connections[username]
    except Exception as e:
        print(f"❌ Erreur WebSocket pour {username}: {e}")
        # Nettoyer les connexions
        if websocket in manager.active_connections:
            manager.active_connections.remove(websocket)
        if username and username in manager.user_connections:
            del manager.user_connections[username]

async def handle_client_message(message: Dict[str, Any], username: str, websocket: WebSocket):
    """Gérer les messages reçus du client"""
    message_type = message.get("type", "unknown")
    print(f"📥 Message reçu de {username}: {message_type}")

    if message_type == "ping":
        # Répondre au ping
        pong_message = {
            "type": "pong",
            "timestamp": datetime.now().isoformat(),
            "original_timestamp": message.get("timestamp")
        }
        await websocket.send_text(json.dumps(pong_message))
        print(f"📤 Pong envoyé à {username}")

    elif message_type == "equipment_status_request":
        # Envoyer le statut des équipements
        equipment_status = {
            "type": "equipment_status",
            "data": [
                {"id": 1, "name": "ICOM IC-R8600", "status": "active", "signal": -65},
                {"id": 2, "name": "Anti-Drone System", "status": "standby", "targets": 0},
                {"id": 3, "name": "ELINT Sensor", "status": "active", "signals": 15},
                {"id": 4, "name": "RF Jammer", "status": "offline", "power": 0}
            ],
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(equipment_status))
        print(f"📤 Statut équipements envoyé à {username}")

    elif message_type == "broadcast":
        # Diffuser un message à tous les utilisateurs connectés
        broadcast_message = {
            "type": "broadcast",
            "message": message.get("message", ""),
            "from_user": username,
            "timestamp": datetime.now().isoformat()
        }
        await manager.broadcast(json.dumps(broadcast_message))
        print(f"📤 Message diffusé par {username}")

    else:
        # Message non reconnu
        error_message = {
            "type": "unknown_message",
            "message": f"Type de message non reconnu: {message_type}",
            "timestamp": datetime.now().isoformat()
        }
        await websocket.send_text(json.dumps(error_message))
        print(f"❓ Message non reconnu de {username}: {message_type}")

async def send_periodic_updates(username: str):
    """Envoyer des mises à jour périodiques"""
    while username in manager.user_connections:
        try:
            # Envoyer des données simulées toutes les 30 secondes
            update_message = {
                "type": "periodic_update",
                "data": {
                    "active_equipment": 3,
                    "alerts_count": 2,
                    "system_status": "operational",
                    "cpu_usage": 45.2,
                    "memory_usage": 67.8
                },
                "timestamp": datetime.now().isoformat()
            }
            await manager.send_personal_message(json.dumps(update_message), username)
            await asyncio.sleep(30)  # Attendre 30 secondes
        except:
            # Connexion fermée, arrêter la boucle
            break
