#!/usr/bin/env python3
"""
Router des logs pour C2-EW Platform
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta

from app.routers.auth import get_current_user, User

router = APIRouter()

# Modèles Pydantic
class Log(BaseModel):
    id: int
    level: str
    message: str
    module: Optional[str] = None
    user_id: Optional[int] = None
    equipment_id: Optional[int] = None
    timestamp: str

# Données mockées des logs
MOCK_LOGS = [
    {
        "id": 1,
        "level": "info",
        "message": "Système démarré avec succès",
        "module": "main",
        "user_id": None,
        "equipment_id": None,
        "timestamp": datetime.now().isoformat()
    },
    {
        "id": 2,
        "level": "warning",
        "message": "Signal faible détecté sur équipement ICOM",
        "module": "equipment",
        "user_id": 1,
        "equipment_id": 1,
        "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat()
    },
    {
        "id": 3,
        "level": "error",
        "message": "Échec de connexion au système anti-drone",
        "module": "equipment",
        "user_id": 2,
        "equipment_id": 2,
        "timestamp": (datetime.now() - timedelta(minutes=10)).isoformat()
    },
    {
        "id": 4,
        "level": "info",
        "message": "Utilisateur admin connecté",
        "module": "auth",
        "user_id": 1,
        "equipment_id": None,
        "timestamp": (datetime.now() - timedelta(minutes=15)).isoformat()
    },
    {
        "id": 5,
        "level": "critical",
        "message": "Intrusion détectée dans la zone de surveillance",
        "module": "security",
        "user_id": None,
        "equipment_id": 3,
        "timestamp": (datetime.now() - timedelta(hours=1)).isoformat()
    }
]

# Routes des logs
@router.get("/", response_model=List[Log])
async def get_logs(
    level: Optional[str] = None,
    module: Optional[str] = None,
    limit: int = 100,
    current_user: User = Depends(get_current_user)
):
    """Récupérer les logs système"""
    logs = MOCK_LOGS.copy()
    
    # Filtrer par niveau si spécifié
    if level:
        logs = [log for log in logs if log["level"] == level]
    
    # Filtrer par module si spécifié
    if module:
        logs = [log for log in logs if log["module"] == module]
    
    # Limiter le nombre de résultats
    logs = logs[:limit]
    
    return [Log(**log) for log in logs]

@router.get("/{log_id}", response_model=Log)
async def get_log_by_id(log_id: int, current_user: User = Depends(get_current_user)):
    """Récupérer un log par ID"""
    log = next((l for l in MOCK_LOGS if l["id"] == log_id), None)
    if not log:
        raise HTTPException(status_code=404, detail="Log non trouvé")
    return Log(**log)

@router.delete("/{log_id}")
async def delete_log(log_id: int, current_user: User = Depends(get_current_user)):
    """Supprimer un log (admin seulement)"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    global MOCK_LOGS
    log = next((l for l in MOCK_LOGS if l["id"] == log_id), None)
    if not log:
        raise HTTPException(status_code=404, detail="Log non trouvé")
    
    MOCK_LOGS = [l for l in MOCK_LOGS if l["id"] != log_id]
    return {"message": "Log supprimé", "log_id": log_id}

@router.delete("/")
async def clear_logs(current_user: User = Depends(get_current_user)):
    """Vider tous les logs (admin seulement)"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    global MOCK_LOGS
    MOCK_LOGS.clear()
    return {"message": "Tous les logs ont été supprimés"}
