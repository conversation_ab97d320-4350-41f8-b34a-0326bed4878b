#!/usr/bin/env python3
"""
Router des alertes pour C2-EW Platform
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.routers.auth import get_current_user, User

router = APIRouter()

# Modèles Pydantic
class Alert(BaseModel):
    id: int
    title: str
    message: str
    level: str
    status: str
    timestamp: str
    equipment_id: Optional[int] = None
    location: Optional[Dict[str, float]] = None

# Données mockées des alertes
MOCK_ALERTS = [
    {
        "id": 1,
        "title": "Signal suspect détecté",
        "message": "Fréquence 446.000 MHz - Signal non identifié",
        "level": "warning",
        "status": "unread",
        "timestamp": datetime.now().isoformat(),
        "equipment_id": 1,
        "location": {"latitude": 48.8566, "longitude": 2.3522}
    },
    {
        "id": 2,
        "title": "Système anti-drone activé",
        "message": "Détection automatique d'un drone hostile",
        "level": "critical",
        "status": "unread",
        "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat(),
        "equipment_id": 2,
        "location": {"latitude": 48.8606, "longitude": 2.3376}
    },
    {
        "id": 3,
        "title": "Maintenance programmée",
        "message": "Maintenance préventive du système ELINT",
        "level": "info",
        "status": "read",
        "timestamp": (datetime.now() - timedelta(hours=2)).isoformat(),
        "equipment_id": 3,
        "location": {"latitude": 48.8534, "longitude": 2.3488}
    }
]

# Routes des alertes
@router.get("/", response_model=List[Alert])
async def get_alerts(current_user: User = Depends(get_current_user)):
    """Récupérer toutes les alertes"""
    return [Alert(**alert) for alert in MOCK_ALERTS]

@router.get("/{alert_id}", response_model=Alert)
async def get_alert_by_id(alert_id: int, current_user: User = Depends(get_current_user)):
    """Récupérer une alerte par ID"""
    alert = next((a for a in MOCK_ALERTS if a["id"] == alert_id), None)
    if not alert:
        raise HTTPException(status_code=404, detail="Alerte non trouvée")
    return Alert(**alert)

@router.patch("/{alert_id}/status")
async def update_alert_status(
    alert_id: int, 
    status: str, 
    current_user: User = Depends(get_current_user)
):
    """Mettre à jour le statut d'une alerte"""
    alert = next((a for a in MOCK_ALERTS if a["id"] == alert_id), None)
    if not alert:
        raise HTTPException(status_code=404, detail="Alerte non trouvée")
    
    alert["status"] = status
    return {"message": "Statut de l'alerte mis à jour", "alert_id": alert_id, "new_status": status}

@router.delete("/{alert_id}")
async def delete_alert(alert_id: int, current_user: User = Depends(get_current_user)):
    """Supprimer une alerte"""
    global MOCK_ALERTS
    alert = next((a for a in MOCK_ALERTS if a["id"] == alert_id), None)
    if not alert:
        raise HTTPException(status_code=404, detail="Alerte non trouvée")
    
    MOCK_ALERTS = [a for a in MOCK_ALERTS if a["id"] != alert_id]
    return {"message": "Alerte supprimée", "alert_id": alert_id}
