from fastapi import FastAP<PERSON>, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import structlog
import os
import json
from datetime import datetime
from pathlib import Path

from app.config import settings
from app.routers import auth, users, equipment, plugins, commands, alerts, geospatial, websocket, logs
from app.api.v1 import line_of_sight

# Configuration du logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Création de l'application FastAPI
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
)

# Configuration des tuiles statiques
tiles_path = os.path.join(os.path.dirname(__file__), "..", "..", "tiles")
if os.path.exists(tiles_path):
    app.mount("/tiles", StaticFiles(directory=tiles_path), name="tiles")
    print(f"📍 Tuiles servies depuis: {tiles_path}")
else:
    print(f"⚠️ Dossier tuiles non trouvé: {tiles_path}")

# Middleware CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permettre toutes les origines pour le debug
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Routes API
app.include_router(auth.router, prefix=f"{settings.API_V1_STR}/auth", tags=["auth"])
app.include_router(users.router, prefix=f"{settings.API_V1_STR}/users", tags=["users"])
app.include_router(equipment.router, prefix=f"{settings.API_V1_STR}/equipment", tags=["equipment"])
app.include_router(plugins.router, prefix=f"{settings.API_V1_STR}/plugins", tags=["plugins"])
app.include_router(commands.router, prefix=f"{settings.API_V1_STR}/commands", tags=["commands"])
app.include_router(alerts.router, prefix=f"{settings.API_V1_STR}/alerts", tags=["alerts"])
app.include_router(logs.router, prefix=f"{settings.API_V1_STR}/logs", tags=["logs"])
app.include_router(geospatial.router, prefix=f"{settings.API_V1_STR}/geo", tags=["geospatial"])
app.include_router(line_of_sight.router, prefix=f"{settings.API_V1_STR}/los", tags=["line-of-sight"])
app.include_router(websocket.router, prefix="/ws", tags=["websocket"])

# Routes compatibilité (sans préfixe API)
app.include_router(equipment.router, prefix="/equipment", tags=["equipment-compat"])

# Route WebSocket compatible frontend
@app.websocket("/ws")
async def websocket_frontend_compatible(websocket: WebSocket):
    """WebSocket compatible avec le frontend existant"""
    print("🔌 Tentative de connexion WebSocket frontend...")

    try:
        # Accepter la connexion immédiatement
        await websocket.accept()
        print("✅ WebSocket frontend connecté !")

        # Envoyer un message de bienvenue
        welcome_message = {
            "type": "welcome",
            "message": "Connexion WebSocket établie",
            "timestamp": datetime.now().isoformat(),
            "user": "frontend_user"
        }
        await websocket.send_text(json.dumps(welcome_message))

        # Boucle de traitement des messages
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                message_type = message.get("type", "unknown")

                print(f"📥 Message frontend: {message_type}")

                if message_type == "ping":
                    pong_message = {
                        "type": "pong",
                        "timestamp": datetime.now().isoformat(),
                        "original_timestamp": message.get("timestamp")
                    }
                    await websocket.send_text(json.dumps(pong_message))

                elif message_type == "equipment_status_request":
                    equipment_status = {
                        "type": "equipment_status",
                        "data": [
                            {"id": 1, "name": "ICOM IC-R8600", "status": "active", "signal": -65},
                            {"id": 2, "name": "Anti-Drone System", "status": "standby", "targets": 0},
                            {"id": 3, "name": "ELINT Sensor", "status": "active", "signals": 15},
                            {"id": 4, "name": "RF Jammer", "status": "offline", "power": 0}
                        ],
                        "timestamp": datetime.now().isoformat()
                    }
                    await websocket.send_text(json.dumps(equipment_status))

                else:
                    # Echo pour les autres messages
                    echo_message = {
                        "type": "echo",
                        "original": message,
                        "timestamp": datetime.now().isoformat()
                    }
                    await websocket.send_text(json.dumps(echo_message))

            except json.JSONDecodeError:
                error_message = {
                    "type": "error",
                    "message": "Format JSON invalide",
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(error_message))

    except Exception as e:
        print(f"❌ Erreur WebSocket frontend: {e}")

# Route WebSocket de test simple
@app.websocket("/ws-test")
async def websocket_test(websocket: WebSocket):
    print("🔌 Tentative de connexion WebSocket test...")
    await websocket.accept()
    print("✅ WebSocket test connecté !")

    try:
        while True:
            # Envoyer un message de test toutes les 5 secondes
            test_message = {
                "type": "test",
                "message": "WebSocket fonctionne !",
                "timestamp": datetime.now().isoformat()
            }
            await websocket.send_text(json.dumps(test_message))

            # Attendre un message du client
            try:
                data = await websocket.receive_text()
                print(f"📥 Message reçu: {data}")

                # Répondre au client
                response = {
                    "type": "echo",
                    "original": data,
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(response))
            except:
                break

    except Exception as e:
        print(f"❌ Erreur WebSocket test: {e}")

# Route de santé
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "C2-EW Backend"}

# Route racine
@app.get("/")
async def root():
    return {
        "message": "C2-EW Platform API",
        "version": settings.VERSION,
        "docs": f"{settings.API_V1_STR}/docs"
    }

# Événements de démarrage et d'arrêt
@app.on_event("startup")
async def startup_event():
    logger.info("🚀 Démarrage de C2-EW Platform API")
    logger.info(f"📍 Version: {settings.VERSION}")
    logger.info(f"🌐 CORS Origins: {settings.BACKEND_CORS_ORIGINS}")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("🛑 Arrêt de C2-EW Platform API")

# Point d'entrée pour uvicorn
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )


