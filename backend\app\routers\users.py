#!/usr/bin/env python3
"""
Router des utilisateurs pour C2-EW Platform
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional

from app.routers.auth import get_current_user, User, MOCK_USERS

router = APIRouter()

# Modèles Pydantic
class UserCreate(BaseModel):
    username: str
    email: str
    full_name: str
    role: str
    password: str

class UserUpdate(BaseModel):
    email: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[str] = None
    is_active: Optional[bool] = None

# Routes des utilisateurs
@router.get("/", response_model=List[User])
async def get_users(current_user: User = Depends(get_current_user)):
    """Récupérer tous les utilisateurs (admin seulement)"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    return [User(**{k: v for k, v in user.items() if k != 'password'}) 
            for user in MOCK_USERS.values()]

@router.get("/{user_id}", response_model=User)
async def get_user_by_id(user_id: int, current_user: User = Depends(get_current_user)):
    """Récupérer un utilisateur par ID"""
    if current_user.role != "admin" and current_user.id != user_id:
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    user = next((u for u in MOCK_USERS.values() if u["id"] == user_id), None)
    if not user:
        raise HTTPException(status_code=404, detail="Utilisateur non trouvé")
    
    return User(**{k: v for k, v in user.items() if k != 'password'})

@router.post("/", response_model=User)
async def create_user(user_data: UserCreate, current_user: User = Depends(get_current_user)):
    """Créer un nouvel utilisateur (admin seulement)"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    if user_data.username in MOCK_USERS:
        raise HTTPException(status_code=400, detail="Nom d'utilisateur déjà existant")
    
    new_id = max([u["id"] for u in MOCK_USERS.values()]) + 1
    new_user = {
        "id": new_id,
        "username": user_data.username,
        "email": user_data.email,
        "full_name": user_data.full_name,
        "role": user_data.role,
        "password": user_data.password,
        "is_active": True
    }
    
    MOCK_USERS[user_data.username] = new_user
    return User(**{k: v for k, v in new_user.items() if k != 'password'})

@router.patch("/{user_id}", response_model=User)
async def update_user(
    user_id: int, 
    user_data: UserUpdate, 
    current_user: User = Depends(get_current_user)
):
    """Mettre à jour un utilisateur"""
    if current_user.role != "admin" and current_user.id != user_id:
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    user = next((u for u in MOCK_USERS.values() if u["id"] == user_id), None)
    if not user:
        raise HTTPException(status_code=404, detail="Utilisateur non trouvé")
    
    # Mettre à jour les champs fournis
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        user[field] = value
    
    return User(**{k: v for k, v in user.items() if k != 'password'})

@router.delete("/{user_id}")
async def delete_user(user_id: int, current_user: User = Depends(get_current_user)):
    """Supprimer un utilisateur (admin seulement)"""
    if current_user.role != "admin":
        raise HTTPException(status_code=403, detail="Accès refusé")
    
    user = next((u for u in MOCK_USERS.values() if u["id"] == user_id), None)
    if not user:
        raise HTTPException(status_code=404, detail="Utilisateur non trouvé")
    
    # Empêcher la suppression de son propre compte
    if user_id == current_user.id:
        raise HTTPException(status_code=400, detail="Impossible de supprimer son propre compte")
    
    # Supprimer l'utilisateur
    username_to_delete = user["username"]
    del MOCK_USERS[username_to_delete]
    
    return {"message": "Utilisateur supprimé", "user_id": user_id}
