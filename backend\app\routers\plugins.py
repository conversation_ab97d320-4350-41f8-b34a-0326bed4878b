#!/usr/bin/env python3
"""
Router des plugins pour C2-EW Platform
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any

from app.routers.auth import get_current_user, User

router = APIRouter()

# Modèles Pydantic
class Plugin(BaseModel):
    id: str
    name: str
    display_name: str
    version: str
    description: str
    status: str
    author: Optional[str] = None
    supported_equipment_types: List[str] = []
    commands: List[Dict[str, Any]] = []

# Données mockées des plugins
MOCK_PLUGINS = [
    {
        "id": "icom-r8600",
        "name": "icom-r8600",
        "display_name": "ICOM IC-R8600",
        "version": "1.0.0",
        "description": "Plugin pour récepteur ICOM IC-R8600",
        "status": "active",
        "author": "C2-EW Team",
        "supported_equipment_types": ["comint"],
        "commands": [
            {"name": "set_frequency", "description": "Changer la fréquence"},
            {"name": "set_mode", "description": "Changer le mode de réception"},
            {"name": "start_recording", "description": "Démarrer l'enregistrement"}
        ]
    },
    {
        "id": "anti-drone-system",
        "name": "anti-drone-system",
        "display_name": "Anti-Drone System",
        "version": "2.1.0",
        "description": "Plugin pour système anti-drone",
        "status": "active",
        "author": "C2-EW Team",
        "supported_equipment_types": ["anti_drone"],
        "commands": [
            {"name": "start_detection", "description": "Démarrer la détection"},
            {"name": "activate_jamming", "description": "Activer le brouillage"},
            {"name": "set_detection_zone", "description": "Définir la zone de détection"}
        ]
    },
    {
        "id": "elint-analyzer",
        "name": "elint-analyzer",
        "display_name": "ELINT Analyzer",
        "version": "1.5.2",
        "description": "Plugin d'analyse ELINT",
        "status": "inactive",
        "author": "C2-EW Team",
        "supported_equipment_types": ["elint"],
        "commands": [
            {"name": "start_analysis", "description": "Démarrer l'analyse"},
            {"name": "set_frequency_range", "description": "Définir la plage de fréquences"},
            {"name": "export_results", "description": "Exporter les résultats"}
        ]
    }
]

# Routes des plugins
@router.get("/", response_model=List[Plugin])
async def get_plugins(current_user: User = Depends(get_current_user)):
    """Récupérer tous les plugins"""
    return [Plugin(**plugin) for plugin in MOCK_PLUGINS]

@router.get("/{plugin_id}", response_model=Plugin)
async def get_plugin_by_id(plugin_id: str, current_user: User = Depends(get_current_user)):
    """Récupérer un plugin par ID"""
    plugin = next((p for p in MOCK_PLUGINS if p["id"] == plugin_id), None)
    if not plugin:
        raise HTTPException(status_code=404, detail="Plugin non trouvé")
    return Plugin(**plugin)

@router.post("/{plugin_id}/activate")
async def activate_plugin(plugin_id: str, current_user: User = Depends(get_current_user)):
    """Activer un plugin"""
    plugin = next((p for p in MOCK_PLUGINS if p["id"] == plugin_id), None)
    if not plugin:
        raise HTTPException(status_code=404, detail="Plugin non trouvé")
    
    plugin["status"] = "active"
    return {"message": f"Plugin {plugin['display_name']} activé", "plugin_id": plugin_id}

@router.post("/{plugin_id}/deactivate")
async def deactivate_plugin(plugin_id: str, current_user: User = Depends(get_current_user)):
    """Désactiver un plugin"""
    plugin = next((p for p in MOCK_PLUGINS if p["id"] == plugin_id), None)
    if not plugin:
        raise HTTPException(status_code=404, detail="Plugin non trouvé")
    
    plugin["status"] = "inactive"
    return {"message": f"Plugin {plugin['display_name']} désactivé", "plugin_id": plugin_id}
