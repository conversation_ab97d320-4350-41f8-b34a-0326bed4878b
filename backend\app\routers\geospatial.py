#!/usr/bin/env python3
"""
Router géospatial pour C2-EW Platform
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import math

from app.routers.auth import get_current_user, User

router = APIRouter()

# Modèles Pydantic
class Point(BaseModel):
    latitude: float
    longitude: float

class Zone(BaseModel):
    id: int
    name: str
    type: str
    coordinates: List[List[float]]
    description: Optional[str] = None

class DistanceCalculation(BaseModel):
    point1: Point
    point2: Point
    distance_m: float
    bearing_deg: float

# Données mockées des zones
MOCK_ZONES = [
    {
        "id": 1,
        "name": "Zone de surveillance A",
        "type": "surveillance",
        "coordinates": [
            [48.8566, 2.3522],
            [48.8600, 2.3600],
            [48.8500, 2.3600],
            [48.8500, 2.3522]
        ],
        "description": "Zone de surveillance principale"
    },
    {
        "id": 2,
        "name": "Zone interdite B",
        "type": "restricted",
        "coordinates": [
            [48.8700, 2.3400],
            [48.8750, 2.3500],
            [48.8650, 2.3500],
            [48.8650, 2.3400]
        ],
        "description": "Zone d'accès restreint"
    }
]

# Fonctions utilitaires
def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculer la distance entre deux points (formule de Haversine)"""
    R = 6371000  # Rayon de la Terre en mètres
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lon = math.radians(lon2 - lon1)
    
    a = (math.sin(delta_lat / 2) ** 2 +
         math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(delta_lon / 2) ** 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    
    return R * c

def calculate_bearing(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculer le cap entre deux points"""
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lon = math.radians(lon2 - lon1)
    
    y = math.sin(delta_lon) * math.cos(lat2_rad)
    x = (math.cos(lat1_rad) * math.sin(lat2_rad) -
         math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(delta_lon))
    
    bearing = math.atan2(y, x)
    return (math.degrees(bearing) + 360) % 360

# Routes géospatiales
@router.get("/zones", response_model=List[Zone])
async def get_zones(current_user: User = Depends(get_current_user)):
    """Récupérer toutes les zones géographiques"""
    return [Zone(**zone) for zone in MOCK_ZONES]

@router.get("/zones/{zone_id}", response_model=Zone)
async def get_zone_by_id(zone_id: int, current_user: User = Depends(get_current_user)):
    """Récupérer une zone par ID"""
    zone = next((z for z in MOCK_ZONES if z["id"] == zone_id), None)
    if not zone:
        raise HTTPException(status_code=404, detail="Zone non trouvée")
    return Zone(**zone)

@router.post("/distance", response_model=DistanceCalculation)
async def calculate_distance_between_points(
    point1: Point, 
    point2: Point, 
    current_user: User = Depends(get_current_user)
):
    """Calculer la distance entre deux points"""
    distance = calculate_distance(
        point1.latitude, point1.longitude,
        point2.latitude, point2.longitude
    )
    bearing = calculate_bearing(
        point1.latitude, point1.longitude,
        point2.latitude, point2.longitude
    )
    
    return DistanceCalculation(
        point1=point1,
        point2=point2,
        distance_m=distance,
        bearing_deg=bearing
    )

@router.post("/point-in-zone")
async def check_point_in_zone(
    point: Point, 
    zone_id: int, 
    current_user: User = Depends(get_current_user)
):
    """Vérifier si un point est dans une zone"""
    zone = next((z for z in MOCK_ZONES if z["id"] == zone_id), None)
    if not zone:
        raise HTTPException(status_code=404, detail="Zone non trouvée")
    
    # Algorithme simple de point dans un polygone (ray casting)
    # Pour une implémentation plus robuste, utiliser une bibliothèque comme Shapely
    coordinates = zone["coordinates"]
    x, y = point.longitude, point.latitude
    n = len(coordinates)
    inside = False
    
    p1x, p1y = coordinates[0]
    for i in range(1, n + 1):
        p2x, p2y = coordinates[i % n]
        if y > min(p1y, p2y):
            if y <= max(p1y, p2y):
                if x <= max(p1x, p2x):
                    if p1y != p2y:
                        xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                    if p1x == p2x or x <= xinters:
                        inside = not inside
        p1x, p1y = p2x, p2y
    
    return {
        "point": point,
        "zone_id": zone_id,
        "zone_name": zone["name"],
        "is_inside": inside
    }
